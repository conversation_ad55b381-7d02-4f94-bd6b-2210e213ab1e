{"name": "@expo/cli", "version": "0.22.26", "description": "The Expo CLI", "main": "build/bin/cli", "types": "build/src/api.d.ts", "exports": {".": {"types": "./build/src/api.d.ts", "default": "./build/src/api.js"}, "./bin/cli": "./build/bin/cli", "./build/src/core/PlatformRegistry": "./build/src/core/PlatformRegistry.js", "./build/src/start/platforms/DeviceManager": "./build/src/start/platforms/DeviceManager.js", "./build/src/start/platforms/PlatformManager": "./build/src/start/platforms/PlatformManager.js", "./build/src/start/platforms/AppIdResolver": "./build/src/start/platforms/AppIdResolver.js", "./src/core/PlatformRegistry": "./build/src/core/PlatformRegistry.js", "./src/start/platforms/DeviceManager": "./build/src/start/platforms/DeviceManager.js", "./src/start/platforms/PlatformManager": "./build/src/start/platforms/PlatformManager.js", "./src/start/platforms/AppIdResolver": "./build/src/start/platforms/AppIdResolver.js"}, "bin": {"expo-internal": "build/bin/cli"}, "files": ["static", "build"], "scripts": {"build": "taskr", "prepare": "taskr release", "clean": "expo-module clean", "lint": "expo-module lint", "typecheck": "expo-module typecheck", "test": "expo-module test", "test:e2e": "jest --config e2e/jest.config.js", "test:playwright": "playwright test --config e2e/playwright.config.ts", "prepublishOnly": "expo-module prepublishOnly", "expo-module": "expo-module", "generate-graphql-code": "graphql-codegen --config graphql-codegen.yml"}, "repository": {"type": "git", "url": "https://github.com/expo/expo.git", "directory": "packages/@expo/cli"}, "keywords": ["expo", "cli"], "author": "Expo", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo/issues"}, "homepage": "https://github.com/expo/expo/tree/main/packages/@expo/cli", "dependencies": {"@0no-co/graphql.web": "^1.0.8", "@babel/runtime": "^7.20.0", "@expo/code-signing-certificates": "^0.0.5", "@expo/config": "~10.0.11", "@expo/config-plugins": "~9.0.17", "@expo/devcert": "^1.1.2", "@expo/env": "~0.4.2", "@expo/image-utils": "^0.6.5", "@expo/json-file": "^9.0.2", "@expo/metro-config": "~0.19.12", "@expo/osascript": "^2.1.6", "@expo/package-manager": "^1.7.2", "@expo/platform-registry": "^1.0.0", "@expo/plist": "^0.2.2", "@expo/prebuild-config": "~8.2.0", "@expo/rudder-sdk-node": "^1.1.1", "@expo/spawn-async": "^1.7.2", "@expo/ws-tunnel": "^1.0.1", "@expo/xcpretty": "^4.3.0", "@react-native/dev-middleware": "0.76.9", "@urql/core": "^5.0.6", "@urql/exchange-retry": "^1.3.0", "accepts": "^1.3.8", "arg": "^5.0.2", "better-opn": "~3.0.2", "bplist-creator": "0.0.7", "bplist-parser": "^0.3.1", "cacache": "^18.0.2", "chalk": "^4.0.0", "ci-info": "^3.3.0", "compression": "^1.7.4", "connect": "^3.7.0", "debug": "^4.3.4", "env-editor": "^0.4.1", "fast-glob": "^3.3.2", "form-data": "^3.0.1", "freeport-async": "^2.0.0", "fs-extra": "~8.1.0", "getenv": "^1.0.0", "glob": "^10.4.2", "internal-ip": "^4.3.0", "is-docker": "^2.0.0", "is-wsl": "^2.1.1", "lodash.debounce": "^4.0.8", "minimatch": "^3.0.4", "node-forge": "^1.3.1", "npm-package-arg": "^11.0.0", "ora": "^3.4.0", "picomatch": "^3.0.1", "pretty-bytes": "^5.6.0", "pretty-format": "^29.7.0", "progress": "^2.0.3", "prompts": "^2.3.2", "qrcode-terminal": "0.11.0", "require-from-string": "^2.0.2", "requireg": "^0.2.2", "resolve": "^1.22.2", "resolve-from": "^5.0.0", "resolve.exports": "^2.0.3", "semver": "^7.6.0", "send": "^0.19.0", "slugify": "^1.3.4", "source-map-support": "~0.5.21", "stacktrace-parser": "^0.1.10", "structured-headers": "^0.4.1", "tar": "^6.2.1", "temp-dir": "^2.0.0", "tempy": "^0.7.1", "terminal-link": "^2.1.1", "undici": "^6.18.2", "unique-string": "~2.0.0", "wrap-ansi": "^7.0.0", "ws": "^8.12.1"}, "taskr": {"requires": ["./taskfile-swc.js"]}, "devDependencies": {"@expo/multipart-body-parser": "^1.0.0", "@expo/ngrok": "4.1.3", "@expo/server": "^0.5.3", "@graphql-codegen/cli": "^2.16.3", "@graphql-codegen/typescript": "^2.8.7", "@graphql-codegen/typescript-operations": "^2.5.12", "@playwright/test": "^1.40.1", "@swc/core": "~1.2.249", "@taskr/clear": "^1.1.0", "@taskr/esnext": "^1.1.0", "@taskr/watch": "^1.1.0", "@types/accepts": "^1.3.5", "@types/cacache": "^17.0.2", "@types/connect": "^3.4.33", "@types/cross-spawn": "^6.0.6", "@types/debug": "^4.1.7", "@types/execa": "^0.9.0", "@types/form-data": "^2.2.0", "@types/getenv": "^1.0.0", "@types/klaw-sync": "^6.0.0", "@types/lodash.debounce": "^4.0.9", "@types/minimatch": "^3.0.5", "@types/node": "^18.19.34", "@types/npm-package-arg": "^6.1.0", "@types/picomatch": "^2.3.3", "@types/progress": "^2.0.5", "@types/prompts": "^2.0.6", "@types/resolve": "^1.20.2", "@types/semver": "^7.5.8", "@types/send": "^0.17.1", "@types/tar": "^6.1.1", "@types/webpack": "~4.41.32", "@types/webpack-dev-server": "^3.11.0", "@types/wrap-ansi": "^8.0.1", "@types/ws": "^8.5.4", "devtools-protocol": "^0.0.1113120", "expo-atlas": "^0.4.0", "expo-module-scripts": "^4.0.5", "find-process": "^1.4.7", "jest-runner-tsd": "^6.0.0", "klaw-sync": "^6.0.0", "memfs": "^3.2.0", "nock": "14.0.0-beta.7", "node-html-parser": "^6.1.5", "nullthrows": "^1.1.1", "playwright": "^1.40.1", "taskr": "^1.1.0", "tree-kill": "^1.2.2", "tsd": "^0.28.1"}}